// Job data with Vietnamese translations
const jobsData = [
    {
        title: "Lifeguard",
        vietnamese: "Nhân viên cứu hộ",
        characteristics: "Responsible, Alert",
        characteristicsVN: "<PERSON><PERSON> trách nhiệm, Tỉnh táo",
        skills: "Swimming, CPR, Communication",
        skillsVN: "<PERSON><PERSON><PERSON> l<PERSON>, <PERSON><PERSON><PERSON> sức tim phổi, <PERSON><PERSON><PERSON>",
        duties: "Monitoring pools, Ensuring safety",
        dutiesVN: "<PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> hồ bơi, <PERSON><PERSON><PERSON> b<PERSON><PERSON> an toàn",
        experience: "Lifeguard certification, First Aid training",
        experienceVN: "Chứng chỉ cứu hộ, Đ<PERSON><PERSON> tạo sơ cứu",
        color: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)"
    },
    {
        title: "Camp Counselor",
        vietnamese: "Hướng dẫn viên trại hè",
        characteristics: "Energetic, Patient",
        characteristicsVN: "Năng động, Kiên nhẫn",
        skills: "Leadership, Communication",
        skillsVN: "<PERSON><PERSON><PERSON> đạo, <PERSON><PERSON><PERSON> tiế<PERSON>",
        duties: "Organizing activities, Supervising kids",
        dutiesVN: "Tổ chức hoạt động, <PERSON><PERSON><PERSON><PERSON> s<PERSON><PERSON> trẻ em",
        experience: "Previous camp experience, CPR training",
        experienceVN: "Kinh nghiệm trại hè, Đào tạo CPR",
        color: "linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%)"
    },
    {
        title: "Babysitter",
        vietnamese: "Người trông trẻ",
        characteristics: "Responsible, Patient",
        characteristicsVN: "Có trách nhiệm, Kiên nhẫn",
        skills: "Childcare, Communication",
        skillsVN: "Chăm sóc trẻ em, Giao tiếp",
        duties: "Caring for children, Playing, Meal prep",
        dutiesVN: "Chăm sóc trẻ em, Chơi đùa, Chuẩn bị bữa ăn",
        experience: "Babysitting experience, References",
        experienceVN: "Kinh nghiệm trông trẻ, Thư giới thiệu",
        color: "linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)"
    },
    {
        title: "Retail Associate",
        vietnamese: "Nhân viên bán hàng",
        characteristics: "Friendly, Customer-oriented",
        characteristicsVN: "Thân thiện, Hướng khách hàng",
        skills: "Communication, Cash handling",
        skillsVN: "Giao tiếp, Xử lý tiền mặt",
        duties: "Assisting customers, Stocking shelves",
        dutiesVN: "Hỗ trợ khách hàng, Sắp xếp kệ hàng",
        experience: "Customer service experience",
        experienceVN: "Kinh nghiệm dịch vụ khách hàng",
        color: "linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)"
    },
    {
        title: "Dog Walker",
        vietnamese: "Người dắt chó đi dạo",
        characteristics: "Responsible, Animal lover",
        characteristicsVN: "Có trách nhiệm, Yêu động vật",
        skills: "Dog handling, Time management",
        skillsVN: "Xử lý chó, Quản lý thời gian",
        duties: "Walking dogs, Basic training",
        dutiesVN: "Dắt chó đi dạo, Huấn luyện cơ bản",
        experience: "Experience with dogs, References",
        experienceVN: "Kinh nghiệm với chó, Thư giới thiệu",
        color: "linear-gradient(135deg, #d299c2 0%, #fef9d7 100%)"
    },
    {
        title: "Tutor",
        vietnamese: "Gia sư",
        characteristics: "Knowledgeable, Patient",
        characteristicsVN: "Hiểu biết, Kiên nhẫn",
        skills: "Teaching, Communication",
        skillsVN: "Giảng dạy, Giao tiếp",
        duties: "Helping with homework, Explaining concepts",
        dutiesVN: "Giúp làm bài tập, Giải thích khái niệm",
        experience: "Strong subject knowledge, Patience",
        experienceVN: "Kiến thức môn học vững, Kiên nhẫn",
        color: "linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%)"
    }
];

// Cohesion and connectors data
const cohesionData = {
    beginnings: [
        "First of all", "To begin with", "Initially", "At first", "In the beginning",
        "Đầu tiên", "Trước hết", "Ban đầu"
    ],
    middle: [
        "Furthermore", "Moreover", "In addition", "Additionally", "Also", "Besides",
        "Hơn nữa", "Ngoài ra", "Bên cạnh đó"
    ],
    endings: [
        "Finally", "In conclusion", "To sum up", "Overall", "In summary",
        "Cuối cùng", "Tóm lại", "Nhìn chung"
    ],
    contrast: [
        "However", "On the other hand", "Nevertheless", "Although", "Despite",
        "Tuy nhiên", "Mặt khác", "Mặc dù"
    ]
};

// Navigation functionality
function showActivity(activityId) {
    // Hide all activities
    document.querySelectorAll('.activity').forEach(activity => {
        activity.classList.remove('active');
    });
    
    // Remove active class from all nav buttons
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // Show selected activity
    document.getElementById(activityId).classList.add('active');
    
    // Add active class to clicked nav button
    document.querySelector(`[data-activity="${activityId}"]`).classList.add('active');
    
    // Initialize activity-specific content
    switch(activityId) {
        case 'job-cards':
            initJobCards();
            break;
        case 'crossword':
            initCrossword();
            break;
        case 'applications':
            initApplications();
            break;
        case 'interviews':
            initInterviews();
            break;
        case 'logic-puzzle':
            initLogicPuzzle();
            break;
        case 'scenarios':
            initScenarios();
            break;
        case 'family-feud':
            initFamilyFeud();
            break;
        case 'jeopardy':
            initJeopardy();
            break;
    }
}

// Activity 1: Job Cards
function initJobCards() {
    const container = document.querySelector('.job-cards-container');
    container.innerHTML = '';
    
    jobsData.forEach((job, index) => {
        const jobCard = document.createElement('div');
        jobCard.className = 'job-card';
        jobCard.style.background = job.color;
        jobCard.style.animationDelay = `${index * 0.1}s`;
        
        jobCard.innerHTML = `
            <div class="job-title">${job.title}</div>
            <div class="job-title-vietnamese">${job.vietnamese}</div>
            <div class="job-details">
                <div class="job-section">
                    <h4>Characteristics / Đặc điểm</h4>
                    <p>${job.characteristics}</p>
                    <p><em>${job.characteristicsVN}</em></p>
                </div>
                <div class="job-section">
                    <h4>Skills Required / Kỹ năng cần thiết</h4>
                    <p>${job.skills}</p>
                    <p><em>${job.skillsVN}</em></p>
                </div>
                <div class="job-section">
                    <h4>Duties / Nhiệm vụ</h4>
                    <p>${job.duties}</p>
                    <p><em>${job.dutiesVN}</em></p>
                </div>
                <div class="job-section">
                    <h4>Experience / Kinh nghiệm</h4>
                    <p>${job.experience}</p>
                    <p><em>${job.experienceVN}</em></p>
                </div>
            </div>
        `;
        
        container.appendChild(jobCard);
    });
    
    // Add word box for reference
    const wordBox = document.createElement('div');
    wordBox.className = 'word-box';
    wordBox.innerHTML = `
        <h3><i class="fas fa-book"></i> Reference Words / Từ tham khảo</h3>
        <div class="word-tags">
            <span class="word-tag">Responsible / Có trách nhiệm</span>
            <span class="word-tag">Patient / Kiên nhẫn</span>
            <span class="word-tag">Energetic / Năng động</span>
            <span class="word-tag">Friendly / Thân thiện</span>
            <span class="word-tag">Alert / Tỉnh táo</span>
            <span class="word-tag">Creative / Sáng tạo</span>
            <span class="word-tag">Reliable / Đáng tin cậy</span>
            <span class="word-tag">Hardworking / Chăm chỉ</span>
        </div>
    `;
    container.appendChild(wordBox);
}

// Initialize the first activity on page load
document.addEventListener('DOMContentLoaded', function() {
    initJobCards();
});

// Crossword data
const crosswordData = {
    grid: [
        ['F','I','R','E','F','I','G','H','T','E','R','','','','',''],
        ['','','','','','','','','','','','','','','',''],
        ['B','A','K','E','R','','','','','','','','','','',''],
        ['','','','','','','','','','','','','','','',''],
        ['B','U','T','C','H','E','R','','','','','','','','',''],
        ['','','','','','','','','','','','','','','',''],
        ['D','O','C','T','O','R','','','','','','','','','',''],
        ['','','','','','','','','','','','','','','',''],
        ['C','H','E','F','','','','','','','','','','','',''],
        ['','','','','','','','T','E','A','C','H','E','R','',''],
        ['C','A','R','P','E','N','T','E','R','','','','','','',''],
        ['','','','','','','','','','','H','A','I','R','D','R'],
        ['','','','','','','','','','','','','','','','E'],
        ['P','O','S','T','M','A','N','','','','','','','','','S'],
        ['','','','','','','','','','','','','','','','S'],
        ['V','E','T','','','','F','A','R','M','E','R','','P','O','L']
    ],
    clues: {
        across: [
            { number: 1, clue: "Someone who fights fire", answer: "FIREFIGHTER", row: 0, col: 0 },
            { number: 9, clue: "Someone who helps us to learn in school", answer: "TEACHER", row: 9, col: 7 },
            { number: 11, clue: "Someone who washes and cuts hair", answer: "HAIRDRESSER", row: 11, col: 10 },
            { number: 13, clue: "Someone who delivers letters", answer: "POSTMAN", row: 13, col: 0 },
            { number: 14, clue: "Someone who helps sick animals", answer: "VET", row: 15, col: 0 },
            { number: 15, clue: "Someone who grows crops", answer: "FARMER", row: 15, col: 6 },
            { number: 16, clue: "Someone who catches criminals", answer: "POLICE", row: 15, col: 13 }
        ],
        down: [
            { number: 2, clue: "Someone who makes bread", answer: "BAKER", row: 2, col: 0 },
            { number: 3, clue: "Someone who cuts up beef", answer: "BUTCHER", row: 4, col: 0 },
            { number: 4, clue: "Someone who helps sick people", answer: "DOCTOR", row: 6, col: 0 },
            { number: 5, clue: "Someone who cooks food in a restaurant", answer: "CHEF", row: 8, col: 0 },
            { number: 6, clue: "A person who makes things out of wood", answer: "CARPENTER", row: 10, col: 0 },
            { number: 7, clue: "Someone who fixes cars", answer: "MECHANIC", row: 0, col: 7 },
            { number: 8, clue: "Someone who catches fish", answer: "FISHERMAN", row: 0, col: 8 },
            { number: 10, clue: "Somebody who fixes teeth", answer: "DENTIST", row: 0, col: 10 },
            { number: 12, clue: "A person who fixes leaking water pipes", answer: "PLUMBER", row: 0, col: 12 }
        ]
    }
};

function initCrossword() {
    const container = document.querySelector('.crossword-container');
    container.innerHTML = `
        <div class="crossword-grid" id="crossword-grid"></div>
        <div class="crossword-clues">
            <div class="clues-section">
                <h3><i class="fas fa-arrow-right"></i> Across</h3>
                <div id="across-clues"></div>
            </div>
            <div class="clues-section">
                <h3><i class="fas fa-arrow-down"></i> Down</h3>
                <div id="down-clues"></div>
            </div>
        </div>
        <div class="word-box">
            <h3><i class="fas fa-lightbulb"></i> Job Vocabulary / Từ vựng nghề nghiệp</h3>
            <div class="word-tags">
                <span class="word-tag">Firefighter / Lính cứu hỏa</span>
                <span class="word-tag">Teacher / Giáo viên</span>
                <span class="word-tag">Doctor / Bác sĩ</span>
                <span class="word-tag">Chef / Đầu bếp</span>
                <span class="word-tag">Police / Cảnh sát</span>
                <span class="word-tag">Farmer / Nông dân</span>
                <span class="word-tag">Baker / Thợ làm bánh</span>
                <span class="word-tag">Carpenter / Thợ mộc</span>
            </div>
        </div>
        <button class="btn" onclick="checkCrossword()">
            <i class="fas fa-check"></i> Check Answers
        </button>
    `;

    createCrosswordGrid();
    populateClues();
}

function createCrosswordGrid() {
    const grid = document.getElementById('crossword-grid');
    const gridSize = 16;

    for (let row = 0; row < gridSize; row++) {
        for (let col = 0; col < gridSize; col++) {
            const cell = document.createElement('div');
            cell.className = 'crossword-cell';

            if (crosswordData.grid[row] && crosswordData.grid[row][col] && crosswordData.grid[row][col] !== '') {
                const input = document.createElement('input');
                input.type = 'text';
                input.maxLength = 1;
                input.dataset.row = row;
                input.dataset.col = col;
                input.dataset.answer = crosswordData.grid[row][col];

                // Add number for clue starts
                const clueNumber = getClueNumber(row, col);
                if (clueNumber) {
                    cell.classList.add('numbered');
                    cell.dataset.number = clueNumber;
                }

                cell.appendChild(input);
            } else {
                cell.classList.add('black');
            }

            grid.appendChild(cell);
        }
    }
}

function getClueNumber(row, col) {
    // Check if this position starts an across or down clue
    const acrossClue = crosswordData.clues.across.find(clue => clue.row === row && clue.col === col);
    const downClue = crosswordData.clues.down.find(clue => clue.row === row && clue.col === col);

    if (acrossClue) return acrossClue.number;
    if (downClue) return downClue.number;
    return null;
}

function populateClues() {
    const acrossContainer = document.getElementById('across-clues');
    const downContainer = document.getElementById('down-clues');

    crosswordData.clues.across.forEach(clue => {
        const clueDiv = document.createElement('div');
        clueDiv.className = 'clue-item';
        clueDiv.innerHTML = `<span class="clue-number">${clue.number}.</span> ${clue.clue}`;
        acrossContainer.appendChild(clueDiv);
    });

    crosswordData.clues.down.forEach(clue => {
        const clueDiv = document.createElement('div');
        clueDiv.className = 'clue-item';
        clueDiv.innerHTML = `<span class="clue-number">${clue.number}.</span> ${clue.clue}`;
        downContainer.appendChild(clueDiv);
    });
}

function checkCrossword() {
    const inputs = document.querySelectorAll('.crossword-cell input');
    let correct = 0;
    let total = 0;

    inputs.forEach(input => {
        total++;
        const userAnswer = input.value.toUpperCase();
        const correctAnswer = input.dataset.answer;

        if (userAnswer === correctAnswer) {
            correct++;
            input.style.background = '#d4edda';
            input.style.color = '#155724';
        } else if (userAnswer !== '') {
            input.style.background = '#f8d7da';
            input.style.color = '#721c24';
        } else {
            input.style.background = '#fff3cd';
            input.style.color = '#856404';
        }
    });

    const percentage = Math.round((correct / total) * 100);
    alert(`You got ${correct} out of ${total} letters correct! (${percentage}%)`);
}

function initApplications() {
    const container = document.querySelector('.applications-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-book"></i> Cohesion & Connectors / Liên kết & Từ nối</h3>
            <div class="word-tags">
                <span class="word-tag">First of all / Đầu tiên</span>
                <span class="word-tag">Furthermore / Hơn nữa</span>
                <span class="word-tag">Moreover / Ngoài ra</span>
                <span class="word-tag">In addition / Thêm vào đó</span>
                <span class="word-tag">However / Tuy nhiên</span>
                <span class="word-tag">Finally / Cuối cùng</span>
                <span class="word-tag">In conclusion / Tóm lại</span>
                <span class="word-tag">Therefore / Do đó</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-users"></i> Pair Work: Job Application Writing</h3>
            <p><strong>Instructions:</strong> Work with your partner to complete job applications using proper connectors and structure.</p>

            <div class="application-templates">
                <div class="application-template">
                    <h4><i class="fas fa-swimming-pool"></i> Lifeguard Application</h4>
                    <div class="application-form">
                        <div class="form-section">
                            <label>Opening paragraph (use a beginning connector):</label>
                            <textarea placeholder="First of all, I am writing to apply for the lifeguard position..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Experience paragraph (use middle connectors):</label>
                            <textarea placeholder="Furthermore, I have experience in... Moreover, I am certified in..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Skills paragraph (use additional connectors):</label>
                            <textarea placeholder="In addition to my experience, I possess... Additionally, I am able to..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Closing paragraph (use ending connectors):</label>
                            <textarea placeholder="Finally, I would like to... In conclusion, I believe..."></textarea>
                        </div>
                    </div>
                </div>

                <div class="application-template">
                    <h4><i class="fas fa-child"></i> Camp Counselor Application</h4>
                    <div class="application-form">
                        <div class="form-section">
                            <label>Opening paragraph:</label>
                            <textarea placeholder="To begin with, I am interested in the camp counselor position..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Experience paragraph:</label>
                            <textarea placeholder="Furthermore, my experience includes... Moreover, I have worked with..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Skills paragraph:</label>
                            <textarea placeholder="Besides my experience, I am skilled in... Also, I can..."></textarea>
                        </div>
                        <div class="form-section">
                            <label>Closing paragraph:</label>
                            <textarea placeholder="To sum up, I am confident that... Overall, I would be..."></textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="pair-work-tips">
                <h4><i class="fas fa-lightbulb"></i> Pair Work Tips</h4>
                <ul>
                    <li><strong>Student A:</strong> Write the opening and skills paragraphs</li>
                    <li><strong>Student B:</strong> Write the experience and closing paragraphs</li>
                    <li><strong>Together:</strong> Review and improve using connectors</li>
                    <li><strong>Switch roles:</strong> Try the other application template</li>
                </ul>
            </div>
        </div>

        <div class="word-box">
            <h3><i class="fas fa-clipboard-list"></i> Application Vocabulary / Từ vựng đơn xin việc</h3>
            <div class="word-tags">
                <span class="word-tag">Apply for / Xin việc</span>
                <span class="word-tag">Position / Vị trí</span>
                <span class="word-tag">Experience / Kinh nghiệm</span>
                <span class="word-tag">Qualification / Bằng cấp</span>
                <span class="word-tag">Skills / Kỹ năng</span>
                <span class="word-tag">Responsible / Có trách nhiệm</span>
                <span class="word-tag">Reliable / Đáng tin cậy</span>
                <span class="word-tag">Available / Có thể làm việc</span>
            </div>
        </div>
    `;
}

function initInterviews() {
    const container = document.querySelector('.interviews-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-comments"></i> Interview Phrases / Cụm từ phỏng vấn</h3>
            <div class="word-tags">
                <span class="word-tag">Tell me about yourself / Hãy kể về bản thân</span>
                <span class="word-tag">Why do you want this job? / Tại sao bạn muốn công việc này?</span>
                <span class="word-tag">What are your strengths? / Điểm mạnh của bạn là gì?</span>
                <span class="word-tag">Do you have experience? / Bạn có kinh nghiệm không?</span>
                <span class="word-tag">When can you start? / Khi nào bạn có thể bắt đầu?</span>
                <span class="word-tag">Any questions for us? / Bạn có câu hỏi nào không?</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-handshake"></i> Interview Role Play</h3>
            <p><strong>Instructions:</strong> Take turns being the interviewer and interviewee. Use the question cards and response guides.</p>

            <div class="interview-setup">
                <div class="role-cards">
                    <div class="role-card interviewer">
                        <h4><i class="fas fa-user-tie"></i> Interviewer Role</h4>
                        <p>You are hiring for summer jobs. Ask questions and evaluate candidates.</p>
                        <button class="btn" onclick="showInterviewQuestions()">Show Question Cards</button>
                    </div>
                    <div class="role-card interviewee">
                        <h4><i class="fas fa-user"></i> Interviewee Role</h4>
                        <p>You are applying for a summer job. Answer questions professionally.</p>
                        <button class="btn" onclick="showResponseGuide()">Show Response Guide</button>
                    </div>
                </div>
            </div>

            <div id="interview-content" class="interview-content"></div>

            <div class="interview-scenarios">
                <h4><i class="fas fa-briefcase"></i> Job Scenarios</h4>
                <div class="scenario-buttons">
                    <button class="btn btn-secondary" onclick="setInterviewJob('lifeguard')">Lifeguard Interview</button>
                    <button class="btn btn-secondary" onclick="setInterviewJob('counselor')">Camp Counselor Interview</button>
                    <button class="btn btn-secondary" onclick="setInterviewJob('retail')">Retail Associate Interview</button>
                    <button class="btn btn-secondary" onclick="setInterviewJob('tutor')">Tutor Interview</button>
                </div>
            </div>
        </div>

        <div class="word-box">
            <h3><i class="fas fa-star"></i> Response Connectors / Từ nối trong câu trả lời</h3>
            <div class="word-tags">
                <span class="word-tag">First of all / Đầu tiên</span>
                <span class="word-tag">For example / Ví dụ</span>
                <span class="word-tag">In my experience / Theo kinh nghiệm của tôi</span>
                <span class="word-tag">Additionally / Thêm vào đó</span>
                <span class="word-tag">Most importantly / Quan trọng nhất</span>
                <span class="word-tag">In conclusion / Tóm lại</span>
            </div>
        </div>
    `;
}

const interviewData = {
    lifeguard: {
        questions: [
            "Why do you want to be a lifeguard?",
            "Do you have swimming experience?",
            "How would you handle an emergency?",
            "Are you available on weekends?",
            "What makes you responsible?"
        ],
        responses: [
            "First of all, I love swimming and water safety...",
            "For example, I have been swimming since I was young...",
            "In my experience, staying calm is most important...",
            "Additionally, I am available on weekends...",
            "Most importantly, I take safety seriously..."
        ]
    },
    counselor: {
        questions: [
            "Why do you want to work with children?",
            "How do you handle difficult kids?",
            "What activities can you lead?",
            "Do you have leadership experience?",
            "How do you ensure safety?"
        ],
        responses: [
            "First of all, I enjoy working with children because...",
            "For example, when children misbehave, I...",
            "In my experience, fun activities include...",
            "Additionally, I have led groups in...",
            "Most importantly, safety comes first..."
        ]
    },
    retail: {
        questions: [
            "Why do you want to work in retail?",
            "How do you handle difficult customers?",
            "Are you good with money?",
            "Can you work in a team?",
            "What are your availability?"
        ],
        responses: [
            "First of all, I enjoy helping customers...",
            "For example, when customers are upset, I...",
            "In my experience, being accurate with money...",
            "Additionally, I work well in teams...",
            "Most importantly, I am flexible with hours..."
        ]
    },
    tutor: {
        questions: [
            "What subjects are you good at?",
            "How do you explain difficult concepts?",
            "Do you have teaching experience?",
            "How do you motivate students?",
            "What's your teaching style?"
        ],
        responses: [
            "First of all, I excel in math and English...",
            "For example, I break down complex problems...",
            "In my experience, patience is key...",
            "Additionally, I encourage students by...",
            "Most importantly, I adapt to each student..."
        ]
    }
};

function showInterviewQuestions() {
    const content = document.getElementById('interview-content');
    content.innerHTML = `
        <div class="question-cards">
            <h4><i class="fas fa-question-circle"></i> General Interview Questions</h4>
            <div class="cards-grid">
                <div class="question-card">Tell me about yourself.</div>
                <div class="question-card">Why do you want this job?</div>
                <div class="question-card">What are your strengths?</div>
                <div class="question-card">What are your weaknesses?</div>
                <div class="question-card">Do you have any experience?</div>
                <div class="question-card">When can you start?</div>
                <div class="question-card">What are your hobbies?</div>
                <div class="question-card">Any questions for us?</div>
            </div>
        </div>
    `;
}

function showResponseGuide() {
    const content = document.getElementById('interview-content');
    content.innerHTML = `
        <div class="response-guide">
            <h4><i class="fas fa-lightbulb"></i> How to Answer Interview Questions</h4>
            <div class="response-tips">
                <div class="tip-card">
                    <h5>Structure your answers:</h5>
                    <p><strong>First of all,</strong> state your main point<br>
                    <strong>For example,</strong> give a specific example<br>
                    <strong>Most importantly,</strong> emphasize key benefits</p>
                </div>
                <div class="tip-card">
                    <h5>Be positive and confident:</h5>
                    <p>Use phrases like "I am excited about..." and "I believe I can..."</p>
                </div>
                <div class="tip-card">
                    <h5>Show enthusiasm:</h5>
                    <p>Express genuine interest in the job and company</p>
                </div>
            </div>
        </div>
    `;
}

function setInterviewJob(jobType) {
    const content = document.getElementById('interview-content');
    const job = interviewData[jobType];

    content.innerHTML = `
        <div class="job-interview">
            <h4><i class="fas fa-briefcase"></i> ${jobType.charAt(0).toUpperCase() + jobType.slice(1)} Interview</h4>
            <div class="interview-columns">
                <div class="questions-column">
                    <h5>Interview Questions:</h5>
                    ${job.questions.map((q, i) => `<div class="question-item">${i + 1}. ${q}</div>`).join('')}
                </div>
                <div class="responses-column">
                    <h5>Sample Response Starters:</h5>
                    ${job.responses.map((r, i) => `<div class="response-item">${i + 1}. ${r}</div>`).join('')}
                </div>
            </div>
        </div>
    `;
}

function initLogicPuzzle() {
    const container = document.querySelector('.logic-puzzle-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-puzzle-piece"></i> Logic Clues / Manh mối logic</h3>
            <div class="word-tags">
                <span class="word-tag">Match / Ghép đôi</span>
                <span class="word-tag">Requirements / Yêu cầu</span>
                <span class="word-tag">Skills / Kỹ năng</span>
                <span class="word-tag">Experience / Kinh nghiệm</span>
                <span class="word-tag">Availability / Thời gian rảnh</span>
                <span class="word-tag">Suitable / Phù hợp</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-brain"></i> Logic Puzzle: Match Job Seekers with Jobs</h3>
            <p><strong>Instructions:</strong> Read the clues and drag job seekers to the correct job advertisements.</p>

            <div class="logic-puzzle-board">
                <div class="job-seekers-section">
                    <h4><i class="fas fa-users"></i> Job Seekers</h4>
                    <div class="seekers-container" id="seekers-container">
                        <!-- Job seekers will be populated here -->
                    </div>
                </div>

                <div class="job-ads-section">
                    <h4><i class="fas fa-newspaper"></i> Job Advertisements</h4>
                    <div class="ads-container" id="ads-container">
                        <!-- Job ads will be populated here -->
                    </div>
                </div>
            </div>

            <div class="logic-clues">
                <h4><i class="fas fa-lightbulb"></i> Logic Clues</h4>
                <div class="clues-list" id="clues-list">
                    <!-- Clues will be populated here -->
                </div>
            </div>

            <button class="btn" onclick="checkLogicPuzzle()">
                <i class="fas fa-check"></i> Check Matches
            </button>
        </div>
    `;

    initLogicPuzzleData();
}

const logicPuzzleData = {
    seekers: [
        {
            id: 'sarah',
            name: 'Sarah',
            description: 'Loves swimming, has CPR certification, very responsible',
            correctJob: 'lifeguard'
        },
        {
            id: 'mike',
            name: 'Mike',
            description: 'Great with children, energetic, has camp experience',
            correctJob: 'counselor'
        },
        {
            id: 'anna',
            name: 'Anna',
            description: 'Friendly personality, good with money, customer service experience',
            correctJob: 'retail'
        },
        {
            id: 'david',
            name: 'David',
            description: 'Excellent in math and English, patient, loves teaching',
            correctJob: 'tutor'
        }
    ],
    jobs: [
        {
            id: 'lifeguard',
            title: 'Lifeguard Position',
            requirements: 'Must have swimming skills and CPR certification. Responsible person needed.',
            location: 'Community Pool'
        },
        {
            id: 'counselor',
            title: 'Camp Counselor',
            requirements: 'Energetic person who loves working with children. Camp experience preferred.',
            location: 'Summer Camp'
        },
        {
            id: 'retail',
            title: 'Retail Associate',
            requirements: 'Friendly person with customer service skills. Must handle money accurately.',
            location: 'Local Store'
        },
        {
            id: 'tutor',
            title: 'Math/English Tutor',
            requirements: 'Strong academic skills in math and English. Patient and good at explaining.',
            location: 'Learning Center'
        }
    ],
    clues: [
        "Sarah has been swimming competitively for 5 years and recently got her CPR certification.",
        "Mike worked as a camp counselor last summer and loves organizing games for kids.",
        "Anna has worked in her family's store and is excellent at handling customer complaints.",
        "David tutored his younger siblings and scored top grades in math and English.",
        "The lifeguard position requires someone who can swim well and handle emergencies.",
        "The camp counselor job needs someone energetic who can supervise children safely.",
        "The retail position requires good people skills and experience with money.",
        "The tutor job needs someone with strong academic skills and patience."
    ]
};

function initLogicPuzzleData() {
    const seekersContainer = document.getElementById('seekers-container');
    const adsContainer = document.getElementById('ads-container');
    const cluesList = document.getElementById('clues-list');

    // Populate job seekers
    logicPuzzleData.seekers.forEach(seeker => {
        const seekerCard = document.createElement('div');
        seekerCard.className = 'seeker-card';
        seekerCard.draggable = true;
        seekerCard.dataset.seekerId = seeker.id;
        seekerCard.innerHTML = `
            <h5><i class="fas fa-user"></i> ${seeker.name}</h5>
            <p>${seeker.description}</p>
        `;

        seekerCard.addEventListener('dragstart', (e) => {
            e.dataTransfer.setData('text/plain', seeker.id);
        });

        seekersContainer.appendChild(seekerCard);
    });

    // Populate job ads
    logicPuzzleData.jobs.forEach(job => {
        const jobCard = document.createElement('div');
        jobCard.className = 'job-ad-card';
        jobCard.dataset.jobId = job.id;
        jobCard.innerHTML = `
            <h5><i class="fas fa-briefcase"></i> ${job.title}</h5>
            <p><strong>Location:</strong> ${job.location}</p>
            <p>${job.requirements}</p>
            <div class="drop-zone" data-job-id="${job.id}">
                <span>Drop job seeker here</span>
            </div>
        `;

        const dropZone = jobCard.querySelector('.drop-zone');
        dropZone.addEventListener('dragover', (e) => {
            e.preventDefault();
            dropZone.classList.add('drag-over');
        });

        dropZone.addEventListener('dragleave', () => {
            dropZone.classList.remove('drag-over');
        });

        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            const seekerId = e.dataTransfer.getData('text/plain');
            const seeker = logicPuzzleData.seekers.find(s => s.id === seekerId);

            dropZone.innerHTML = `<span><i class="fas fa-user"></i> ${seeker.name}</span>`;
            dropZone.dataset.assignedSeeker = seekerId;
            dropZone.classList.remove('drag-over');
            dropZone.classList.add('has-assignment');
        });

        adsContainer.appendChild(jobCard);
    });

    // Populate clues
    logicPuzzleData.clues.forEach((clue, index) => {
        const clueItem = document.createElement('div');
        clueItem.className = 'clue-item';
        clueItem.innerHTML = `<span class="clue-number">${index + 1}.</span> ${clue}`;
        cluesList.appendChild(clueItem);
    });
}

function checkLogicPuzzle() {
    const dropZones = document.querySelectorAll('.drop-zone');
    let correct = 0;
    let total = 0;

    dropZones.forEach(zone => {
        const jobId = zone.dataset.jobId;
        const assignedSeeker = zone.dataset.assignedSeeker;

        if (assignedSeeker) {
            total++;
            const seeker = logicPuzzleData.seekers.find(s => s.id === assignedSeeker);

            if (seeker && seeker.correctJob === jobId) {
                correct++;
                zone.classList.add('correct-match');
                zone.classList.remove('incorrect-match');
            } else {
                zone.classList.add('incorrect-match');
                zone.classList.remove('correct-match');
            }
        }
    });

    const percentage = total > 0 ? Math.round((correct / total) * 100) : 0;
    alert(`You got ${correct} out of ${total} matches correct! (${percentage}%)`);
}

function initScenarios() {
    const container = document.querySelector('.scenarios-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-search"></i> Scenario Clues / Manh mối kịch bản</h3>
            <div class="word-tags">
                <span class="word-tag">Job Advertisement / Quảng cáo việc làm</span>
                <span class="word-tag">Job Application / Đơn xin việc</span>
                <span class="word-tag">Requirements / Yêu cầu</span>
                <span class="word-tag">Qualifications / Bằng cấp</span>
                <span class="word-tag">Seeking / Tìm kiếm</span>
                <span class="word-tag">Applying / Nộp đơn</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-question-circle"></i> Job Ad or Application? Guess the Scenario!</h3>
            <p><strong>Instructions:</strong> Read each scenario and decide if it's a job advertisement or job application. Then guess the job!</p>

            <div class="scenario-game">
                <div class="scenario-progress">
                    <span>Question <span id="current-question">1</span> of <span id="total-questions">10</span></span>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill"></div>
                    </div>
                </div>

                <div class="scenario-content" id="scenario-content">
                    <!-- Scenario content will be populated here -->
                </div>

                <div class="scenario-score" id="scenario-score">
                    Score: <span id="score-value">0</span> / <span id="score-total">0</span>
                </div>
            </div>
        </div>
    `;

    initScenariosGame();
}

const scenariosData = [
    {
        text: "We are looking for a responsible lifeguard to work at our community pool. Must have CPR certification and strong swimming skills. Apply today!",
        type: "Job Advertisement",
        job: "Lifeguard",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Lifeguard", "Swimming Instructor", "Pool Cleaner", "Camp Counselor"]
    },
    {
        text: "Dear Hiring Manager, I am writing to apply for the camp counselor position. I have experience working with children and am very energetic.",
        type: "Job Application",
        job: "Camp Counselor",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Camp Counselor", "Teacher", "Babysitter", "Coach"]
    },
    {
        text: "Retail store seeks friendly sales associate. Must be good with customers and available weekends. Competitive hourly wage offered.",
        type: "Job Advertisement",
        job: "Sales Associate",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Sales Associate", "Cashier", "Store Manager", "Customer Service"]
    },
    {
        text: "I am interested in the tutoring position. I have excellent grades in math and English and enjoy helping other students learn.",
        type: "Job Application",
        job: "Tutor",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Tutor", "Teacher", "Study Buddy", "Academic Advisor"]
    },
    {
        text: "Dog walking service needs reliable person who loves animals. Flexible hours, great pay, and you get to work with adorable dogs!",
        type: "Job Advertisement",
        job: "Dog Walker",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Dog Walker", "Pet Sitter", "Veterinarian", "Animal Trainer"]
    },
    {
        text: "To whom it may concern, I would like to apply for the babysitting job. I am responsible, patient, and have experience caring for my younger siblings.",
        type: "Job Application",
        job: "Babysitter",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Babysitter", "Nanny", "Childcare Worker", "Camp Counselor"]
    },
    {
        text: "Ice cream shop hiring enthusiastic scooper! Must be friendly, energetic, and available summer evenings. Tips included!",
        type: "Job Advertisement",
        job: "Ice Cream Scooper",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Ice Cream Scooper", "Server", "Cashier", "Food Service Worker"]
    },
    {
        text: "Dear Sir/Madam, I am applying for the lawn care position. I am hardworking, reliable, and have experience with yard maintenance equipment.",
        type: "Job Application",
        job: "Lawn Care Worker",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Lawn Care Worker", "Gardener", "Landscaper", "Groundskeeper"]
    },
    {
        text: "Summer camp seeks creative arts and crafts instructor. Must be artistic, patient with children, and able to lead fun activities.",
        type: "Job Advertisement",
        job: "Arts Instructor",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Arts Instructor", "Art Teacher", "Camp Counselor", "Activity Leader"]
    },
    {
        text: "I am writing to express my interest in the pet sitting position. I love animals, am very responsible, and have cared for pets before.",
        type: "Job Application",
        job: "Pet Sitter",
        options: ["Job Advertisement", "Job Application"],
        jobOptions: ["Pet Sitter", "Dog Walker", "Animal Caretaker", "Veterinary Assistant"]
    }
];

let currentScenario = 0;
let scenarioScore = 0;

function initScenariosGame() {
    currentScenario = 0;
    scenarioScore = 0;
    document.getElementById('total-questions').textContent = scenariosData.length;
    document.getElementById('score-total').textContent = scenariosData.length * 2; // 2 points per question
    showScenario();
}

function showScenario() {
    if (currentScenario >= scenariosData.length) {
        showScenariosResults();
        return;
    }

    const scenario = scenariosData[currentScenario];
    const content = document.getElementById('scenario-content');

    content.innerHTML = `
        <div class="scenario-text">
            <h4><i class="fas fa-file-text"></i> Scenario ${currentScenario + 1}</h4>
            <p>"${scenario.text}"</p>
        </div>

        <div class="scenario-questions">
            <div class="question-section">
                <h5>1. Is this a Job Advertisement or Job Application?</h5>
                <div class="scenario-options">
                    ${scenario.options.map(option =>
                        `<button class="option-btn" onclick="answerScenarioType('${option}')">${option}</button>`
                    ).join('')}
                </div>
            </div>

            <div class="question-section" id="job-question" style="display: none;">
                <h5>2. What job is this about?</h5>
                <div class="scenario-options">
                    ${scenario.jobOptions.map(job =>
                        `<button class="option-btn" onclick="answerScenarioJob('${job}')">${job}</button>`
                    ).join('')}
                </div>
            </div>
        </div>
    `;

    updateScenarioProgress();
}

function answerScenarioType(answer) {
    const scenario = scenariosData[currentScenario];
    const buttons = document.querySelectorAll('.question-section:first-child .option-btn');

    buttons.forEach(btn => {
        btn.disabled = true;
        if (btn.textContent === answer) {
            if (answer === scenario.type) {
                btn.classList.add('correct');
                scenarioScore++;
            } else {
                btn.classList.add('incorrect');
            }
        } else if (btn.textContent === scenario.type) {
            btn.classList.add('correct');
        }
    });

    // Show job question
    document.getElementById('job-question').style.display = 'block';
    updateScenarioScore();
}

function answerScenarioJob(answer) {
    const scenario = scenariosData[currentScenario];
    const buttons = document.querySelectorAll('#job-question .option-btn');

    buttons.forEach(btn => {
        btn.disabled = true;
        if (btn.textContent === answer) {
            if (answer === scenario.job) {
                btn.classList.add('correct');
                scenarioScore++;
            } else {
                btn.classList.add('incorrect');
            }
        } else if (btn.textContent === scenario.job) {
            btn.classList.add('correct');
        }
    });

    updateScenarioScore();

    // Move to next scenario after delay
    setTimeout(() => {
        currentScenario++;
        showScenario();
    }, 2000);
}

function updateScenarioProgress() {
    document.getElementById('current-question').textContent = currentScenario + 1;
    const progress = ((currentScenario) / scenariosData.length) * 100;
    document.getElementById('progress-fill').style.width = progress + '%';
}

function updateScenarioScore() {
    document.getElementById('score-value').textContent = scenarioScore;
}

function showScenariosResults() {
    const content = document.getElementById('scenario-content');
    const totalPossible = scenariosData.length * 2;
    const percentage = Math.round((scenarioScore / totalPossible) * 100);

    content.innerHTML = `
        <div class="results-screen">
            <h4><i class="fas fa-trophy"></i> Game Complete!</h4>
            <div class="final-score">
                <p>Your Score: ${scenarioScore} / ${totalPossible}</p>
                <p>Percentage: ${percentage}%</p>
            </div>
            <button class="btn" onclick="initScenariosGame()">
                <i class="fas fa-redo"></i> Play Again
            </button>
        </div>
    `;
}

function initFamilyFeud() {
    const container = document.querySelector('.family-feud-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-users"></i> Team Game Vocabulary / Từ vựng trò chơi nhóm</h3>
            <div class="word-tags">
                <span class="word-tag">Survey says / Khảo sát cho thấy</span>
                <span class="word-tag">Top answer / Câu trả lời hàng đầu</span>
                <span class="word-tag">Strike / Sai</span>
                <span class="word-tag">Points / Điểm</span>
                <span class="word-tag">Team / Đội</span>
                <span class="word-tag">Buzzer / Chuông</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-trophy"></i> Family Feud: Jobs Edition</h3>
            <p><strong>Instructions:</strong> Choose the most popular answers! Work in teams and see who can get the highest score.</p>

            <div class="feud-game">
                <div class="feud-header">
                    <div class="team-scores">
                        <div class="team-score">
                            <h4>Team A</h4>
                            <span id="team-a-score">0</span>
                        </div>
                        <div class="current-round">
                            <span>Round <span id="feud-round">1</span></span>
                            <div class="strikes">
                                <span>Strikes: </span>
                                <span id="strike-display">0</span>/3
                            </div>
                        </div>
                        <div class="team-score">
                            <h4>Team B</h4>
                            <span id="team-b-score">0</span>
                        </div>
                    </div>
                </div>

                <div class="feud-board" id="feud-board">
                    <!-- Game board will be populated here -->
                </div>

                <div class="feud-controls">
                    <button class="btn" onclick="nextFeudQuestion()">
                        <i class="fas fa-forward"></i> Next Question
                    </button>
                    <button class="btn btn-secondary" onclick="resetFeudGame()">
                        <i class="fas fa-redo"></i> New Game
                    </button>
                </div>
            </div>
        </div>
    `;

    initFeudGame();
}

const familyFeudData = [
    {
        question: "Name a job that requires good communication skills",
        answers: [
            { text: "Teacher", points: 35 },
            { text: "Sales Person", points: 25 },
            { text: "Customer Service", points: 20 },
            { text: "Manager", points: 12 },
            { text: "Receptionist", points: 8 }
        ]
    },
    {
        question: "Name a job that helps people",
        answers: [
            { text: "Doctor", points: 40 },
            { text: "Nurse", points: 25 },
            { text: "Teacher", points: 15 },
            { text: "Police Officer", points: 12 },
            { text: "Firefighter", points: 8 }
        ]
    },
    {
        question: "Name a summer job for teenagers",
        answers: [
            { text: "Lifeguard", points: 30 },
            { text: "Camp Counselor", points: 25 },
            { text: "Ice Cream Scooper", points: 20 },
            { text: "Babysitter", points: 15 },
            { text: "Lawn Care", points: 10 }
        ]
    },
    {
        question: "Name a job that requires special training",
        answers: [
            { text: "Doctor", points: 35 },
            { text: "Pilot", points: 25 },
            { text: "Electrician", points: 20 },
            { text: "Chef", points: 12 },
            { text: "Hairdresser", points: 8 }
        ]
    },
    {
        question: "Name a job where you work with animals",
        answers: [
            { text: "Veterinarian", points: 40 },
            { text: "Dog Walker", points: 25 },
            { text: "Pet Sitter", points: 15 },
            { text: "Farmer", points: 12 },
            { text: "Zoo Keeper", points: 8 }
        ]
    }
];

let currentFeudQuestion = 0;
let teamAScore = 0;
let teamBScore = 0;
let currentTeam = 'A';
let strikes = 0;
let revealedAnswers = [];

function initFeudGame() {
    currentFeudQuestion = 0;
    teamAScore = 0;
    teamBScore = 0;
    currentTeam = 'A';
    strikes = 0;
    revealedAnswers = [];

    updateFeudScores();
    showFeudQuestion();
}

function showFeudQuestion() {
    if (currentFeudQuestion >= familyFeudData.length) {
        showFeudResults();
        return;
    }

    const question = familyFeudData[currentFeudQuestion];
    const board = document.getElementById('feud-board');

    board.innerHTML = `
        <div class="feud-question">
            <h4><i class="fas fa-question-circle"></i> ${question.question}</h4>
        </div>

        <div class="feud-answers">
            ${question.answers.map((answer, index) => `
                <div class="feud-answer" id="answer-${index}">
                    <div class="answer-number">${index + 1}</div>
                    <div class="answer-text" style="display: none;">${answer.text}</div>
                    <div class="answer-points" style="display: none;">${answer.points}</div>
                </div>
            `).join('')}
        </div>

        <div class="feud-options">
            <h5>Team ${currentTeam}, choose your answer:</h5>
            <div class="answer-choices">
                ${question.answers.map((answer, index) => `
                    <button class="option-btn" onclick="selectFeudAnswer(${index})">${answer.text}</button>
                `).join('')}
            </div>
        </div>
    `;

    document.getElementById('feud-round').textContent = currentFeudQuestion + 1;
    revealedAnswers = [];
    strikes = 0;
    updateStrikeDisplay();
}

function selectFeudAnswer(answerIndex) {
    const question = familyFeudData[currentFeudQuestion];
    const answer = question.answers[answerIndex];

    if (revealedAnswers.includes(answerIndex)) {
        alert('This answer has already been revealed!');
        return;
    }

    // Reveal the answer
    revealedAnswers.push(answerIndex);
    const answerElement = document.getElementById(`answer-${answerIndex}`);
    answerElement.classList.add('revealed');
    answerElement.querySelector('.answer-text').style.display = 'block';
    answerElement.querySelector('.answer-points').style.display = 'block';

    // Add points to current team
    if (currentTeam === 'A') {
        teamAScore += answer.points;
    } else {
        teamBScore += answer.points;
    }

    updateFeudScores();

    // Disable the selected button
    const buttons = document.querySelectorAll('.answer-choices .option-btn');
    buttons[answerIndex].disabled = true;
    buttons[answerIndex].classList.add('selected');

    // Check if all answers revealed
    if (revealedAnswers.length === question.answers.length) {
        setTimeout(() => {
            currentFeudQuestion++;
            showFeudQuestion();
        }, 2000);
    } else {
        // Switch teams
        currentTeam = currentTeam === 'A' ? 'B' : 'A';
        document.querySelector('.feud-options h5').textContent = `Team ${currentTeam}, choose your answer:`;
    }
}

function updateFeudScores() {
    document.getElementById('team-a-score').textContent = teamAScore;
    document.getElementById('team-b-score').textContent = teamBScore;
}

function updateStrikeDisplay() {
    document.getElementById('strike-display').textContent = strikes;
}

function nextFeudQuestion() {
    currentFeudQuestion++;
    showFeudQuestion();
}

function resetFeudGame() {
    initFeudGame();
}

function showFeudResults() {
    const board = document.getElementById('feud-board');
    const winner = teamAScore > teamBScore ? 'Team A' : teamBScore > teamAScore ? 'Team B' : 'Tie';

    board.innerHTML = `
        <div class="feud-results">
            <h4><i class="fas fa-trophy"></i> Game Over!</h4>
            <div class="final-scores">
                <div class="final-score">
                    <h5>Team A</h5>
                    <span>${teamAScore} points</span>
                </div>
                <div class="winner-announcement">
                    <h3>${winner === 'Tie' ? "It's a Tie!" : `${winner} Wins!`}</h3>
                </div>
                <div class="final-score">
                    <h5>Team B</h5>
                    <span>${teamBScore} points</span>
                </div>
            </div>
        </div>
    `;
}

function initJeopardy() {
    const container = document.querySelector('.jeopardy-container');
    container.innerHTML = `
        <div class="word-box">
            <h3><i class="fas fa-trophy"></i> Jeopardy Vocabulary / Từ vựng Jeopardy</h3>
            <div class="word-tags">
                <span class="word-tag">Category / Danh mục</span>
                <span class="word-tag">Question / Câu hỏi</span>
                <span class="word-tag">Answer / Câu trả lời</span>
                <span class="word-tag">Points / Điểm</span>
                <span class="word-tag">Daily Double / Nhân đôi hàng ngày</span>
                <span class="word-tag">Final Jeopardy / Jeopardy cuối</span>
            </div>
        </div>

        <div class="game-container">
            <h3><i class="fas fa-trophy"></i> Job Jeopardy</h3>
            <p><strong>Instructions:</strong> Choose a category and point value. Answer questions about jobs, cohesion, and structure!</p>

            <div class="jeopardy-game">
                <div class="jeopardy-score">
                    <span>Score: <span id="jeopardy-score">0</span> points</span>
                </div>

                <div class="jeopardy-board" id="jeopardy-board">
                    <!-- Jeopardy board will be populated here -->
                </div>

                <div class="jeopardy-question" id="jeopardy-question" style="display: none;">
                    <!-- Current question will be displayed here -->
                </div>

                <div class="jeopardy-controls">
                    <button class="btn btn-secondary" onclick="resetJeopardyGame()">
                        <i class="fas fa-redo"></i> New Game
                    </button>
                </div>
            </div>
        </div>
    `;

    initJeopardyGame();
}

const jeopardyData = {
    categories: [
        {
            name: "Job Skills",
            questions: [
                {
                    points: 100,
                    question: "This skill involves talking and listening to others effectively.",
                    options: ["Communication", "Leadership", "Organization", "Creativity"],
                    correct: 0
                },
                {
                    points: 200,
                    question: "The ability to guide and motivate a team is called this.",
                    options: ["Management", "Leadership", "Supervision", "Direction"],
                    correct: 1
                },
                {
                    points: 300,
                    question: "This skill helps you manage your time and tasks efficiently.",
                    options: ["Planning", "Organization", "Scheduling", "Coordination"],
                    correct: 1
                },
                {
                    points: 400,
                    question: "The ability to think of new ideas and solutions is called this.",
                    options: ["Innovation", "Creativity", "Imagination", "Invention"],
                    correct: 1
                },
                {
                    points: 500,
                    question: "This skill involves working well with others in a group.",
                    options: ["Cooperation", "Teamwork", "Collaboration", "Partnership"],
                    correct: 1
                }
            ]
        },
        {
            name: "Cohesion Words",
            questions: [
                {
                    points: 100,
                    question: "Which connector shows addition? 'I like swimming. _____, I enjoy running.'",
                    options: ["However", "Furthermore", "Therefore", "Although"],
                    correct: 1
                },
                {
                    points: 200,
                    question: "Which word shows contrast? 'I want the job. _____, I lack experience.'",
                    options: ["Moreover", "However", "Additionally", "Furthermore"],
                    correct: 1
                },
                {
                    points: 300,
                    question: "Which phrase starts a conclusion? '_____, I believe I'm qualified.'",
                    options: ["First of all", "For example", "In conclusion", "Moreover"],
                    correct: 2
                },
                {
                    points: 400,
                    question: "Which connector shows cause and effect? 'I studied hard. _____, I got good grades.'",
                    options: ["However", "Although", "Therefore", "Besides"],
                    correct: 2
                },
                {
                    points: 500,
                    question: "Which phrase introduces an example? '_____, I worked as a lifeguard last summer.'",
                    options: ["In conclusion", "For instance", "However", "Therefore"],
                    correct: 1
                }
            ]
        },
        {
            name: "Job Applications",
            questions: [
                {
                    points: 100,
                    question: "The first paragraph of a job application should include this.",
                    options: ["Your age", "The position you want", "Your hobbies", "Your address"],
                    correct: 1
                },
                {
                    points: 200,
                    question: "This section describes your previous work experience.",
                    options: ["Introduction", "Experience paragraph", "Conclusion", "Skills section"],
                    correct: 1
                },
                {
                    points: 300,
                    question: "You should end your application with this type of paragraph.",
                    options: ["Experience", "Skills", "Closing/Conclusion", "Introduction"],
                    correct: 2
                },
                {
                    points: 400,
                    question: "This word means 'able to be trusted' in job applications.",
                    options: ["Energetic", "Reliable", "Creative", "Friendly"],
                    correct: 1
                },
                {
                    points: 500,
                    question: "This phrase means 'I can work at different times'.",
                    options: ["I am reliable", "I am available", "I am flexible", "I am responsible"],
                    correct: 2
                }
            ]
        },
        {
            name: "Summer Jobs",
            questions: [
                {
                    points: 100,
                    question: "This job requires swimming skills and CPR certification.",
                    options: ["Camp Counselor", "Lifeguard", "Swimming Instructor", "Pool Cleaner"],
                    correct: 1
                },
                {
                    points: 200,
                    question: "This job involves organizing activities for children at summer camp.",
                    options: ["Lifeguard", "Camp Counselor", "Teacher", "Babysitter"],
                    correct: 1
                },
                {
                    points: 300,
                    question: "This job requires being friendly and serving customers frozen treats.",
                    options: ["Waiter", "Ice Cream Scooper", "Cashier", "Cook"],
                    correct: 1
                },
                {
                    points: 400,
                    question: "This job involves helping students with homework and explaining concepts.",
                    options: ["Teacher", "Tutor", "Counselor", "Coach"],
                    correct: 1
                },
                {
                    points: 500,
                    question: "This job requires love for animals and responsibility for their care.",
                    options: ["Veterinarian", "Pet Sitter", "Animal Trainer", "Zoo Keeper"],
                    correct: 1
                }
            ]
        }
    ]
};

let jeopardyScore = 0;
let answeredQuestions = [];

function initJeopardyGame() {
    jeopardyScore = 0;
    answeredQuestions = [];
    updateJeopardyScore();
    createJeopardyBoard();
}

function createJeopardyBoard() {
    const board = document.getElementById('jeopardy-board');

    let boardHTML = '<div class="jeopardy-categories">';

    // Create category headers
    jeopardyData.categories.forEach(category => {
        boardHTML += `<div class="jeopardy-category-header">${category.name}</div>`;
    });

    boardHTML += '</div>';

    // Create question grid
    for (let i = 0; i < 5; i++) {
        boardHTML += '<div class="jeopardy-row">';
        jeopardyData.categories.forEach((category, catIndex) => {
            const question = category.questions[i];
            const questionId = `${catIndex}-${i}`;
            const isAnswered = answeredQuestions.includes(questionId);

            boardHTML += `
                <div class="jeopardy-cell ${isAnswered ? 'answered' : ''}"
                     onclick="${isAnswered ? '' : `showJeopardyQuestion(${catIndex}, ${i})`}">
                    ${isAnswered ? '' : question.points}
                </div>
            `;
        });
        boardHTML += '</div>';
    }

    board.innerHTML = boardHTML;
}

function showJeopardyQuestion(categoryIndex, questionIndex) {
    const category = jeopardyData.categories[categoryIndex];
    const question = category.questions[questionIndex];
    const questionContainer = document.getElementById('jeopardy-question');

    questionContainer.innerHTML = `
        <div class="question-display">
            <div class="question-category">${category.name} - ${question.points} points</div>
            <div class="question-text">${question.question}</div>
            <div class="question-options">
                ${question.options.map((option, index) => `
                    <button class="option-btn" onclick="answerJeopardyQuestion(${categoryIndex}, ${questionIndex}, ${index})">
                        ${option}
                    </button>
                `).join('')}
            </div>
        </div>
    `;

    questionContainer.style.display = 'block';
    document.getElementById('jeopardy-board').style.display = 'none';
}

function answerJeopardyQuestion(categoryIndex, questionIndex, selectedAnswer) {
    const category = jeopardyData.categories[categoryIndex];
    const question = category.questions[questionIndex];
    const questionId = `${categoryIndex}-${questionIndex}`;

    const buttons = document.querySelectorAll('.question-options .option-btn');
    buttons.forEach((btn, index) => {
        btn.disabled = true;
        if (index === question.correct) {
            btn.classList.add('correct');
        } else if (index === selectedAnswer && selectedAnswer !== question.correct) {
            btn.classList.add('incorrect');
        }
    });

    if (selectedAnswer === question.correct) {
        jeopardyScore += question.points;
        setTimeout(() => {
            alert(`Correct! You earned ${question.points} points!`);
        }, 500);
    } else {
        setTimeout(() => {
            alert(`Incorrect! The correct answer was: ${question.options[question.correct]}`);
        }, 500);
    }

    answeredQuestions.push(questionId);
    updateJeopardyScore();

    setTimeout(() => {
        document.getElementById('jeopardy-question').style.display = 'none';
        document.getElementById('jeopardy-board').style.display = 'block';
        createJeopardyBoard();

        if (answeredQuestions.length === jeopardyData.categories.length * 5) {
            showJeopardyResults();
        }
    }, 3000);
}

function updateJeopardyScore() {
    document.getElementById('jeopardy-score').textContent = jeopardyScore;
}

function resetJeopardyGame() {
    initJeopardyGame();
}

function showJeopardyResults() {
    const board = document.getElementById('jeopardy-board');
    const maxPossible = jeopardyData.categories.length * (100 + 200 + 300 + 400 + 500);
    const percentage = Math.round((jeopardyScore / maxPossible) * 100);

    board.innerHTML = `
        <div class="jeopardy-results">
            <h4><i class="fas fa-trophy"></i> Game Complete!</h4>
            <div class="final-jeopardy-score">
                <p>Final Score: ${jeopardyScore} points</p>
                <p>Percentage: ${percentage}%</p>
                <p>Maximum Possible: ${maxPossible} points</p>
            </div>
            <div class="performance-message">
                ${percentage >= 80 ? "Excellent work! You're a job expert!" :
                  percentage >= 60 ? "Good job! You know your stuff!" :
                  percentage >= 40 ? "Not bad! Keep studying!" :
                  "Keep practicing! You'll get better!"}
            </div>
        </div>
    `;
}
